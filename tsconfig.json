{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "strict": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "skipLibCheck": true, "strictNullChecks": true, "strictBindCallApply": false, "noImplicitAny": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@app/jwt": ["libs/jwt/src"], "@app/jwt/*": ["libs/jwt/src/*"], "@app/crypto": ["libs/crypto/src"], "@app/crypto/*": ["libs/crypto/src/*"], "@app/redis": ["libs/redis/src"], "@app/redis/*": ["libs/redis/src/*"], "@app/database": ["libs/database/src"], "@app/database/*": ["libs/database/src/*"], "@app/base": ["libs/base/src"], "@app/base/*": ["libs/base/src/*"], "@app/decorators": ["libs/decorators/src"], "@app/decorators/*": ["libs/decorators/src/*"], "@app/filters": ["libs/filters/src"], "@app/filters/*": ["libs/filters/src/*"], "@app/guards": ["libs/guards/src"], "@app/guards/*": ["libs/guards/src/*"], "@app/interceptors": ["libs/interceptors/src"], "@app/interceptors/*": ["libs/interceptors/src/*"], "@app/helpers": ["libs/helpers/src"], "@app/helpers/*": ["libs/helpers/src/*"], "@app/enums": ["libs/enums/src"], "@app/enums/*": ["libs/enums/src/*"], "@app/interfaces": ["libs/interfaces/src"], "@app/interfaces/*": ["libs/interfaces/src/*"], "@app/constants": ["libs/constants/src"], "@app/constants/*": ["libs/constants/src/*"]}}}