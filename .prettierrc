{"arrowParens": "always", "bracketSameLine": false, "bracketSpacing": true, "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": false, "proseWrap": "always", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleAttributePerLine": false, "singleQuote": true, "tabWidth": 4, "trailingComma": "none", "useTabs": true, "vueIndentScriptAndStyle": false, "printWidth": 100}