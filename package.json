{"name": "nestjs-base", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"libs/**/*.ts\"", "start": "NODE_ENV=production nest start", "start:dev": "NODE_ENV=development nest start --watch", "start:debug": "NODE_ENV=development nest start --debug --watch", "start:prod": "NODE_ENV=production node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "NODE_ENV=test jest", "test:watch": "NODE_ENV=test jest --watch", "test:cov": "NODE_ENV=test jest --coverage", "test:debug": "NODE_ENV=test node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "NODE_ENV=test jest --config ./test/jest-e2e.json", "prepare": "patch-package && husky install"}, "dependencies": {"@nestjs/common": "^9.0.0", "@nestjs/config": "^2.3.3", "@nestjs/core": "^9.0.0", "@nestjs/devtools-integration": "^0.1.4", "@nestjs/jwt": "^10.0.3", "@nestjs/passport": "^9.0.3", "@nestjs/platform-express": "^9.0.0", "@nestjs/platform-socket.io": "^9.4.3", "@nestjs/serve-static": "^3.0.1", "@nestjs/swagger": "^6.3.0", "@nestjs/typeorm": "^9.0.1", "@nestjs/websockets": "^9.4.3", "@types/multer": "^1.4.7", "@types/passport-jwt": "^3.0.8", "@types/passport-local": "^1.0.35", "add": "^2.0.6", "argon2": "^0.30.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cookie-parser": "^1.4.6", "husky": "^8.0.3", "ioredis": "^5.3.2", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "patch-package": "^7.0.0", "pg": "^8.11.0", "postinstall-postinstall": "^2.1.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.2.0", "socket.io": "^4.6.2", "ts-loader": "^9.4.3", "tsc-alias": "^1.8.6", "typeorm": "^0.3.16", "yarn": "^1.22.19"}, "devDependencies": {"@commitlint/cli": "^17.6.5", "@commitlint/config-conventional": "^17.6.5", "@darraghor/eslint-plugin-nestjs-typed": "^3.22.6", "@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/cookie-parser": "^1.4.3", "@types/express": "^4.17.13", "@types/jest": "29.5.0", "@types/node": "18.15.11", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.5.0", "lint-staged": "^13.2.2", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.0.5", "ts-node": "^10.0.0", "tsconfig-paths": "4.2.0", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/src/", "<rootDir>/libs/"], "moduleNameMapper": {"^@app/jwt(|/.*)$": "<rootDir>/libs/jwt/src/$1", "^@app/crypto(|/.*)$": "<rootDir>/libs/crypto/src/$1", "^@app/redis(|/.*)$": "<rootDir>/libs/redis/src/$1", "^@app/database(|/.*)$": "<rootDir>/libs/database/src/$1", "^@app/base(|/.*)$": "<rootDir>/libs/base/src/$1", "^@app/decorators(|/.*)$": "<rootDir>/libs/decorators/src/$1", "^@app/filters(|/.*)$": "<rootDir>/libs/filters/src/$1", "^@app/guards(|/.*)$": "<rootDir>/libs/guards/src/$1", "^@app/interceptors(|/.*)$": "<rootDir>/libs/interceptors/src/$1", "^@app/helpers(|/.*)$": "<rootDir>/libs/helpers/src/$1", "^@app/enums(|/.*)$": "<rootDir>/libs/enums/src/$1", "^@app/interfaces(|/.*)$": "<rootDir>/libs/interfaces/src/$1", "^@app/constants(|/.*)$": "<rootDir>/libs/constants/src/$1"}}, "lint-staged": {"*.ts": ["npm run lint", "npm run format"]}}